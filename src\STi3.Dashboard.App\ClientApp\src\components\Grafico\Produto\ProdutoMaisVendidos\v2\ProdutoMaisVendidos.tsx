import React, { useState, useEffect, useCallback } from 'react';
import HeaderCard from 'components/HeaderCard';
import { useChartData } from 'store/ChartDataContext';
import IdentificacaoGrafico from 'constants/identificacaoGrafico';
import GraficoSemDados from 'components/GraficoSemDados';
import TotalizadorDividido from 'components/TotalizadorDividido';
import Totalizador from 'components/Totalizador';
import DatePickerRange from 'components/DatePickerRange';
import dateToDayDatePicker from 'helpers/dateFormat';
import { DayRange } from 'react-modern-calendar-datepicker';

import formatarDinheiro from 'helpers/moneyFormat';
import { Divider, makeStyles, Typography } from '@material-ui/core';
import { maxHourDate, minHourDate } from 'helpers/dateMinMax';

const useStyles = makeStyles(() => ({
  container: {
    background: '#993A50',
    padding: '5px 10px 0px 10px',
    margin: '0px 10px 0px 10px',
  },
  observacao: {
    padding: '0px 10px 5px 10px',
    fontSize: '12px',
  },
  divider: {
    margin: '0px 10px 0px 10px',
    background: '#FE6383',
  },
  porcentagem: {
    display: 'flex',
    alignItems: 'center',
    height: '100%',
    fontWeight: 'bold',
  },
  descricao: {
    color: (props: any) => props.cor,
    fontSize: '14px',
    paddingBottom: '5px 0 5px 0',
    display: 'flex',
    justifyContent: 'space-between',
    fontWeight: 'bold',
  },
  obs: {
    color: (props: any) => props.cor,
    fontSize: '12px',
    paddingBottom: '5px 0 5px 0',
    display: 'flex',
    justifyContent: 'space-between',
  },
  quantidade: {
    fontSize: '12px',
    marginBottom: '5px',
    minWidth: '80px',
    textAlign: 'right',
    fontWeight: 'bold',
  },
  valorTotal: {
    fontSize: '12px',
    marginBottom: '5px',
    minWidth: '80px',
    textAlign: 'right',
  },
}));

const ProdutoMaisVendidos = () => {
  const { chartData } = useChartData();
  const [dataUltimaSincronizacao, setDataUltimaSincronizacao] = useState('');
  const [totalVendas, setTotalVendas] = useState(0);
  const [totalVendasAdicional, setTotalVendasAdicional] = useState(0);
  const [defaultDateRange, setDefaultDateRange] = useState<DayRange>({
    from: dateToDayDatePicker(new Date()),
    to: dateToDayDatePicker(new Date()),
  });

  const [legenda, setLegenda] = useState([] as Array<any>);

  const obterDadosGrafico = useCallback((dados) => {
    const informacoesFiltro = dados.filter(
      (item) =>
        item.identificacao ===
        IdentificacaoGrafico.Produtos_ProdutosMaisVendidos
    );

    if (informacoesFiltro.length > 0) {
      return {
        ...informacoesFiltro[0],
      };
    }

    return null;
  }, []);

  const obterDadosPorFiltro = useCallback(
    (dtInicial, dtFinal) => {
      const informacao = obterDadosGrafico(chartData);

      const informacoesFiltro = informacao.dados.filter((item) => {
        const dataItem = new Date(item.Data);
        return (
          dtInicial &&
          dtFinal &&
          dataItem >= minHourDate(dtInicial) &&
          dataItem <= maxHourDate(dtFinal)
        );
      });

      let valTotal = 0;
      let valTotalAdicional = 0;

      let listItens = Array<any>();

      for (let index = 0; index < informacoesFiltro.length; index += 1) {
        const infoFiltro = informacoesFiltro[index];
        valTotal += infoFiltro.Total;
        valTotalAdicional += infoFiltro.TotalAdicional;

        for (
          let indexDado = 0;
          indexDado < infoFiltro.Dados.length;
          indexDado += 1
        ) {
          const dado = infoFiltro.Dados[indexDado];

          const objIndex = listItens.findIndex(
            (obj) =>
              obj.descricao === dado.Descricao &&
              obj.obs === dado.InformacaoAdicional[0].Descricao
          );

          if (objIndex === -1) {
            listItens.push({
              id: listItens.length - 1,
              descricao: dado.Descricao,
              obs: dado.InformacaoAdicional[0].Descricao,
              valorTotal: dado.InformacaoAdicional[0].Total,
              quantidade: dado.Total,
            });
          } else {
            const itemUpdate = listItens[objIndex];

            listItens[objIndex] = {
              id: itemUpdate.id,
              descricao: dado.Descricao,
              obs: dado.InformacaoAdicional[0].Descricao,
              valorTotal:
                itemUpdate.valorTotal + dado.InformacaoAdicional[0].Total,
              quantidade: itemUpdate.quantidade + dado.Total,
            };
          }
        }
      }

      setTotalVendas(valTotal);
      setTotalVendasAdicional(valTotalAdicional);

      listItens = listItens.sort(
        (a, b) => parseFloat(b.quantidade) - parseFloat(a.quantidade)
      );

      listItens = listItens.slice(
        0,
        listItens.length > 50 ? 50 : listItens.length
      );

      setLegenda(listItens);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [chartData]
  );

  const resetGrafico = () => {
    setTotalVendas(0);
  };

  useEffect(() => {
    const informacao = obterDadosGrafico(chartData);

    if (informacao) {
      const dateTo = new Date(informacao.dataUltimaSincronizacao);
      const dateFrom = new Date(informacao.dataUltimaSincronizacao);

      setDataUltimaSincronizacao(dateTo.toLocaleString());

      setDefaultDateRange({
        from: dateToDayDatePicker(dateFrom),
        to: dateToDayDatePicker(dateTo),
      });

      const filtros = informacao.dados.map((item, index) => {
        return {
          texto: item.Data,
          valor: item.Total,
          possuiDados: item.Total > 0,
          key: index,
        };
      });

      if (filtros) {
        const filtrosComValor = filtros.filter((filtro) => filtro.possuiDados);
        if (filtrosComValor.length > 0) {
          obterDadosPorFiltro(dateFrom, dateTo);
        } else {
          resetGrafico();
        }
      }
    } else {
      resetGrafico();
    }
  }, [chartData, obterDadosPorFiltro, obterDadosGrafico]);

  const classes = useStyles({ cor: 'white' });

  const dateOnChange = (selectedDate: { from: Date; to: Date }) => {
    obterDadosPorFiltro(selectedDate.from, selectedDate.to);
  };

  const InfoGrafico = (props) => {
    const { descricao, obs, valorTotal, quantidade } = props;

    return (
      <>
        <div className={classes.container}>
          <div className={classes.descricao}>
            <div>{descricao}</div>
            <div className={classes.quantidade}>
              <p>{quantidade}</p>
            </div>
          </div>
          <div
            style={{
              paddingBottom: '5px 0 5px 0',
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <div className={classes.obs}>{obs}</div>
            <div className={classes.valorTotal}>
              {formatarDinheiro(valorTotal, true)}
            </div>
          </div>
        </div>
        <Divider className={classes.divider} />
      </>
    );
  };
  return (
    <>
      <HeaderCard
        titulo="Produtos Mais Vendidos"
        atualizadoEm={dataUltimaSincronizacao}
        exibirFiltro={false}
        handleOrdem={() => {}}
      />

      <DatePickerRange
        bgButtonColor="#FE6383"
        bgDateColor="#993A50"
        onChange={dateOnChange}
        defaultDateRange={defaultDateRange}
      />

      {
        // TODO Rever regra após atualização dos clientes
        totalVendasAdicional ? (
          <>
            <TotalizadorDividido
              valorEsquerdo={totalVendas.toFixed(2)}
              tituloEsquerdo="Quantidade Total"
              exibirEmReaisEsquerdo={false}
              valorDireito={totalVendasAdicional}
              tituloDireito="Valor Total"
              exibirEmReaisDireito
            />
            <Typography className={classes.observacao}>
              Top 50 produtos mais vendidos.
            </Typography>
          </>
        ) : (
          <>
            <Totalizador
              valor={totalVendas}
              titulo="Total de Itens Vendidos"
              exibirEmReais={false}
            />
            <Typography className={classes.observacao}>
              Top 50 produtos mais vendidos.
            </Typography>
          </>
        )
      }

      <div style={{ marginBottom: '4px' }}>
        {totalVendas === 0 && <GraficoSemDados />}
        {totalVendas > 0 && (
          <div style={{ paddingBottom: '5px' }}>
            {legenda.map((item) => (
              <InfoGrafico
                key={item.id}
                descricao={item.descricao}
                valorTotal={item.valorTotal}
                obs={item.obs}
                quantidade={
                  item.quantidade % 1 > 0
                    ? item.quantidade.toFixed(2)
                    : item.quantidade
                }
              />
            ))}
          </div>
        )}
      </div>
    </>
  );
};

export default ProdutoMaisVendidos;
