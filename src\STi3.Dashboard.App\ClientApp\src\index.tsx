import React, { Suspense } from 'react';
import { createRoot } from 'react-dom/client';
import Loading from 'components/ProgressBar';
import App from './App';
import * as serviceWorkerRegistration from './serviceWorkerRegistration';

const container = document.getElementById('root');
const root = createRoot(container!);

root.render(
  <React.StrictMode>
    <Suspense fallback={<Loading />}>
      <App />
    </Suspense>
  </React.StrictMode>
);

serviceWorkerRegistration.register({
  onUpdate: (registration) => {
    if (registration && registration.waiting) {
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      const evt = new CustomEvent('pwaUpdated');
      window.dispatchEvent(evt);
    }
  },
});
// reportWebVitals();
