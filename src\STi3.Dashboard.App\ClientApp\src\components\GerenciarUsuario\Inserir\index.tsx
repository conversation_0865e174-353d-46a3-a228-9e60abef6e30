import React, { useCallback, useEffect, useState } from 'react';
import {
  createStyles,
  Theme,
  makeStyles,
  withStyles,
} from '@material-ui/core/styles';
import Dialog from '@material-ui/core/Dialog';
import {
  AppBar,
  Button,
  Checkbox,
  colors,
  DialogContent,
  FormControlLabel,
  FormHelperText,
  Grid,
  IconButton,
  Popover,
  TextField,
  Toolbar,
  Typography,
} from '@material-ui/core';
import { ArrowBack, HelpOutline } from '@material-ui/icons';
import Transition from 'components/TransitionDefault';
import auth from '../../../auth';
import FormInput from 'components/FormInput';
import { useForm } from 'react-hook-form';
import ButtonLoading from 'components/Button/ButtonLoading';
import FormInputPassword from 'components/FormInputPassword';
import api, { ResponseApi } from 'services/api';
import ConstanteEnderecoWebservice from 'constants/enderecoWebservice';
import CheckBoxOutlineBlankIcon from '@material-ui/icons/CheckBoxOutlineBlank';
import CheckBoxIcon from '@material-ui/icons/CheckBox';
import FormInputAutoComplete from 'components/FormInputAutoComplete';
import ConstanteMensagemValidacao from 'constants/mensagensValidacoes';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { useSnackbar } from 'notistack';
import Loading from 'components/Loading';

const useStyles = makeStyles((theme: Theme) =>
  createStyles({
    appBar: {
      position: 'relative',
      background: theme.palette.primary.main,
    },
    title: {
      marginLeft: theme.spacing(2),
      flex: 1,
    },
    body: {
      height: '100%',
      '& .MuiDialogContent-root': {
        padding: '0px',
        background: colors.grey[800],
      },
    },
    dialog: {
      background: colors.blueGrey[800],
      color: colors.common.white,
    },
    form: {
      background: colors.grey[800],
      padding: theme.spacing(1, 2, 1, 2),
      height: '100%',
    },
    formControl: {
      marginTop: theme.spacing(2),
    },
    radioButton: {
      display: 'flex',
      alignItems: 'center',
    },
    helpMessage: {
      background: colors.grey[700],
    },
    typographyHelp: {
      padding: theme.spacing(2),
    },
  })
);

interface LojaInterface {
  id: string;
  nomeFantasia: string;
  cidade: string;
  uf: string;
}

const CssTextField = withStyles((theme) => ({
  root: {
    '& :not(.Mui-error)': {
      '&.Mui-focused.MuiInputLabel-asterisk, &.Mui-focused.MuiInputLabel-outlined':
        {
          color: theme.palette.primary.main,
        },
      '&.MuiInputLabel-outlined': {
        color: colors.grey[400],
        '&.MuiInputLabel-asterisk': {
          color: colors.grey[400],
        },
      },

      '&.MuiOutlinedInput-root': {
        '& fieldset': {
          borderColor: colors.grey[300],
          color: colors.grey[300],
        },
        '&:hover fieldset :not(.Mui-disabled)': {
          borderColor: theme.palette.primary.main,
        },
        '&.Mui-focused fieldset': {
          borderColor: theme.palette.primary.main,
        },
        '& input:disabled': {
          color: colors.grey[200],
          background: '#2e3b42',
        },
      },
    },
    '& .MuiFormLabel-root.Mui-disabled': {
      color: colors.grey[300],
    },
  },
}))(TextField);

const icon = <CheckBoxOutlineBlankIcon fontSize="small" color="secondary" />;
const checkedIcon = <CheckBoxIcon fontSize="small" color="primary" />;

const schema = yup.object().shape({
  nome: yup.string().required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO),
  email: yup
    .string()
    .required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO)
    .email(ConstanteMensagemValidacao.EMAIL_INVALIDO),
  senha: yup
    .string()
    .required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO)
    .min(6, ConstanteMensagemValidacao.SENHA_MINIMO_SEIS_CARACTERES),
  confirmarSenha: yup
    .string()
    .required(ConstanteMensagemValidacao.CAMPO_OBRIGATORIO)
    .oneOf(
      [yup.ref('senha'), ''],
      ConstanteMensagemValidacao.SENHA_NAO_COINCIDEM
    ),
  lojas: yup.array().min(1, ConstanteMensagemValidacao.CAMPO_OBRIGATORIO),
});

interface PropsDialog {
  dialogOpen: boolean;
  setDialogOpen: React.Dispatch<React.SetStateAction<boolean>>;
  handleDialogClose: () => void;
}

const InserirUsuario = ({
  dialogOpen,
  setDialogOpen,
  handleDialogClose,
}: PropsDialog) => {
  const classes = useStyles();
  const [lojas, setLojas] = useState([] as Array<LojaInterface>);
  const [anchorElHelp, setAnchorElHelp] = React.useState(null);
  const [showLojas, setShowLojas] = React.useState(true);
  const { enqueueSnackbar } = useSnackbar();
  const [loading, setLoading] = React.useState(false);

  const { handleSubmit, control, setValue, register, errors, trigger, reset } =
    useForm({
      resolver: yupResolver(schema),
      defaultValues: {
        nome: '',
        email: '',
        senha: '',
        confirmarSenha: '',
        administrador: false,
        lojas: [] as Array<LojaInterface>,
      },
    });

  const openHelp = Boolean(anchorElHelp);

  const handleClickOpenHelp = (event) => {
    setAnchorElHelp(event.currentTarget);
  };

  const handleClickCloseHelp = () => {
    setAnchorElHelp(null);
  };

  const handleCheckAdminsitrador = (event) => {
    setShowLojas(!event.target.checked);
  };

  const handleClose = () => {
    setDialogOpen(false);
  };

  const getLojas = useCallback(async () => {
    setLoading(true);
    const response = await api.get<void, ResponseApi<Array<LojaInterface>>>(
      ConstanteEnderecoWebservice.LOJA_LISTAR,
      {
        params: {
          empresaId: auth.empresaId(),
        },
      }
    );

    setLoading(false);
    if (response.sucesso) {
      setLojas(response.dados);
    }

    if (response.avisos) {
      response.avisos.map((item: string) =>
        enqueueSnackbar(item, { variant: 'warning' })
      );
    }
  }, [enqueueSnackbar]);

  const handleVincularTodasLojas = () => {
    setValue('lojas', lojas);
    if (errors.lojas) trigger();
  };

  const onSubmit = handleSubmit(async (data) => {
    setLoading(true);

    let lojasId = [] as Array<any>;

    if (lojas.length > 1) {
      lojasId = data.lojas ? data.lojas.map((item) => item.id) : [];
    } else {
      lojasId = lojas.map((item) => item.id);
    }

    const response = await api.post<void, ResponseApi>(
      ConstanteEnderecoWebservice.USUARIO_INSERIR,
      {
        administrador: data.administrador,
        nome: data.nome,
        email: data.email,
        senha: data.senha,
        empresaId: auth.empresaId(),
        lojasId,
      }
    );

    setLoading(false);

    if (response.sucesso) {
      enqueueSnackbar('Operação realizada com sucesso.', {
        variant: 'success',
      });

      handleDialogClose();
      setDialogOpen(false);
    }

    if (response.avisos) {
      response.avisos.map((item: string) =>
        enqueueSnackbar(item, { variant: 'warning' })
      );
    }
  });

  const resetForm = useCallback(() => {
    reset({
      nome: '',
      email: '',
      senha: '',
      confirmarSenha: '',
      administrador: false,
      lojas: [] as Array<LojaInterface>,
    });

    setShowLojas(true);
  }, [reset]);

  useEffect(() => {
    if (dialogOpen) {
      resetForm();

      if (lojas.length === 0) {
        getLojas();
      }
    }
  }, [dialogOpen, getLojas, lojas, resetForm]);

  return (
    <Dialog
      fullScreen
      open={dialogOpen}
      onClose={handleClose}
      TransitionComponent={Transition}
      className={classes.body}
    >
      {loading && <Loading />}
      <AppBar className={classes.appBar}>
        <Toolbar>
          <IconButton
            edge="start"
            color="inherit"
            onClick={handleClose}
            aria-label="close"
          >
            <ArrowBack />
          </IconButton>
          <Typography variant="h6" className={classes.title}>
            Adicionar usuário
          </Typography>
          <ButtonLoading
            text="Salvar"
            loading={false}
            size="small"
            fullWidth={false}
            variant="text"
            color="secondary"
            cor={colors.common.white}
            onClick={onSubmit}
          />
        </Toolbar>
      </AppBar>
      <DialogContent>
        <form className={classes.form}>
          <Grid container spacing={2}>
            <Grid item className={classes.radioButton} xs={12} md={2}>
              <div>
                <FormControlLabel
                  control={
                    <Checkbox
                      color="primary"
                      inputRef={register}
                      name="administrador"
                      defaultChecked={false}
                      onClick={handleCheckAdminsitrador}
                    />
                  }
                  label="Administrador"
                />
                <IconButton size="small" onClick={handleClickOpenHelp}>
                  <HelpOutline fontSize="small" />
                </IconButton>
                <Popover
                  classes={{ paper: classes.helpMessage }}
                  open={openHelp}
                  anchorEl={anchorElHelp}
                  onClose={handleClickCloseHelp}
                  anchorOrigin={{
                    vertical: 'bottom',
                    horizontal: 'right',
                  }}
                  transformOrigin={{
                    vertical: 'top',
                    horizontal: 'left',
                  }}
                >
                  <Typography className={classes.typographyHelp}>
                    O usuário administrador é vinculado automaticamente com
                    todas as lojas e possui acesso em todas as funcionalidades
                    do aplicativo.
                  </Typography>
                </Popover>
              </div>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormInput
                required
                variant="outlined"
                margin="normal"
                fullWidth
                label="Nome"
                name="nome"
                autoFocus
                control={control}
                setValue={setValue}
                error={Boolean(errors.nome)}
                helperText={errors.nome?.message}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormInput
                required
                variant="outlined"
                margin="normal"
                fullWidth
                label="E-mail"
                name="email"
                control={control}
                setValue={setValue}
                error={Boolean(errors.email)}
                helperText={errors.email?.message}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormInputPassword
                required
                name="senha"
                label="Senha"
                control={control}
                error={Boolean(errors.senha)}
                messageError={errors.senha?.message}
                labelWidth={56}
              />
            </Grid>
            <Grid item xs={12} md={2}>
              <FormInputPassword
                required
                name="confirmarSenha"
                label="Confirmar senha"
                control={control}
                error={Boolean(errors.confirmarSenha)}
                messageError={errors.confirmarSenha?.message}
                labelWidth={130}
              />
            </Grid>
            {showLojas && lojas.length > 1 && (
              <Grid item xl={12} xs={12}>
                <Button
                  onClick={handleVincularTodasLojas}
                  style={{ float: 'right' }}
                  color="primary"
                >
                  Vincular com todas as lojas
                </Button>
                <FormInputAutoComplete
                  control={control}
                  name="lojas"
                  defaultValue={[]}
                  noOptionsText="Loja não encontrada"
                  options={lojas}
                  getOptionLabel={(option) =>
                    `${option.nomeFantasia} (${option.cidade} - ${option.uf})`
                  }
                  renderOption={(option, { selected }) => (
                    <>
                      <Checkbox
                        icon={icon}
                        checkedIcon={checkedIcon}
                        style={{ marginRight: 8 }}
                        checked={selected}
                      />
                      {`${option.nomeFantasia} (${option.cidade} - ${option.uf})`}
                    </>
                  )}
                  renderInput={(params) => (
                    <CssTextField
                      {...params}
                      variant="outlined"
                      label="Lojas"
                      required
                      error={Boolean(errors.lojas)}
                    />
                  )}
                />
                {Boolean(errors.lojas) && (
                  <FormHelperText className="Mui-error MuiFormHelperText-contained">
                    {ConstanteMensagemValidacao.CAMPO_OBRIGATORIO}
                  </FormHelperText>
                )}
              </Grid>
            )}
          </Grid>

          <div />
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default InserirUsuario;
