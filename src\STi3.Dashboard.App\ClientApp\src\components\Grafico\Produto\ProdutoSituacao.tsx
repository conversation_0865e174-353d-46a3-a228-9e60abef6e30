import React, { useState, useEffect, useCallback } from 'react';
import HeaderCard from 'components/HeaderCard';
import { useChartData } from 'store/ChartDataContext';
import IdentificacaoGrafico from 'constants/identificacaoGrafico';
import ReactApexChart from 'react-apexcharts';
import ApexCharts from 'apexcharts';
import GraficoSemDados from 'components/GraficoSemDados';
import LegendaGrafico from 'components/LegendaGrafico';

const ProdutoSituacao = () => {
  const { chartData } = useChartData();
  const [dataUltimaSincronizacao, setDataUltimaSincronizacao] = useState('');
  const [infoGraficoState, setInfoGraficoState] = useState<any>(undefined);
  const [totalVendas, setTotalVendas] = useState(0);
  const [legenda, setLegenda] = useState([] as Array<any>);

  const obterDadosGrafico = (dados) => {
    const informacoesFiltro = dados.filter(
      (item) =>
        item.identificacao === IdentificacaoGrafico.Produtos_SituacaoProdutos
    );

    if (informacoesFiltro.length > 0) {
      return {
        ...informacoesFiltro[0],
      };
    }

    return null;
  };

  const atualizarGrafico = (list, total) => {
    const colors = [
      '#008FFB',
      '#00E396',
      '#FEB019',
      '#FF4560',
      '#775DD0',
      '#03A9F4',
      '#4CAF50',
      '#F9CE1D',
      '#FF9800',
      '#33B2DF',
      '#7D02EB',
    ];

    const listPorcentagem = list.map((obj, index) => ({
      ...obj,
      id: index,
      porcentagem: obj.valor === 0 ? 0 : obj.valor / (total / 100),
    }));

    const listSort = listPorcentagem.sort(
      (a, b) => parseFloat(b.porcentagem) - parseFloat(a.porcentagem)
    );

    setLegenda(
      listSort.map((item, index) => ({
        cor: colors[index <= colors.length - 1 ? index : index % colors.length],
        formaPagto: item.nome,
        valor: item.valor,
        porcentagem: `${item.porcentagem.toFixed(1)}%`,
      }))
    );

    const infoGrafico = {
      series: listSort.map((item) => item.porcentagem),

      options: {
        stroke: {
          lineCap: 'round',
        },
        chart: {
          id: 'situacao-produto',
        },
        colors,
        labels: listSort.map((item) => item.nome),
        legend: {
          show: false,
          position: 'bottom',
          horizontalAlign: 'center',
          floating: false,
          labels: {
            colors: '#fff',
            useSeriesColors: false,
          },
          itemMargin: {
            horizontal: 10,
            vertical: 5,
          },
          onItemClick: {
            toggleDataSeries: false,
          },
        },
        tooltip: {
          enabled: true,
          y: {
            formatter: (val) => `${val.toFixed(1)}%`,
          },
        },
      },
    };

    const newInfoGrafico = {
      ...infoGrafico,
      series: listSort.map((item) => item.porcentagem),
      options: {
        ...infoGrafico.options,
        labels: listSort.map((item) => item.nome),
      },
    };

    // Necessário para atualização do gráfico
    try {
      ApexCharts.exec(
        newInfoGrafico.options.chart.id,
        'updateOptions',
        newInfoGrafico.options
      );

      ApexCharts.exec(newInfoGrafico.options.chart.id, 'resetSeries');

      // eslint-disable-next-line no-empty
    } catch (error) {}

    setInfoGraficoState(newInfoGrafico);
  };

  const obterDados = useCallback(() => {
    setInfoGraficoState(undefined);
    const informacao = obterDadosGrafico(chartData).dados[0];

    const Itens = informacao.Dados.map((item, index) => {
      return {
        id: index,
        nome: item.Descricao,
        valor: item.Total,
      };
    });

    setTotalVendas(informacao.Total);
    atualizarGrafico(Itens, informacao.Total);
  }, [chartData]);

  const resetGrafico = () => {
    setTotalVendas(0);
  };

  useEffect(() => {
    const informacao = obterDadosGrafico(chartData);

    if (informacao) {
      setDataUltimaSincronizacao(
        new Date(informacao.dataUltimaSincronizacao).toLocaleString()
      );

      obterDados();
    } else {
      resetGrafico();
    }
  }, [chartData, obterDados]);

  return (
    <>
      <HeaderCard
        titulo="Situação do Estoque"
        atualizadoEm={dataUltimaSincronizacao}
        exibirFiltro={false}
        handleOrdem={() => {}}
      />

      <div style={{ marginBottom: '4px' }}>
        {totalVendas === 0 && <GraficoSemDados />}
        {totalVendas > 0 && (
          <div style={{ paddingBottom: '5px' }}>
            <ReactApexChart
              options={infoGraficoState.options}
              series={infoGraficoState.series}
              type="pie"
              height="300px"
            />
            {legenda.map((item) => (
              <LegendaGrafico
                key={item.formaPagto}
                cor={item.cor}
                descricao={item.formaPagto}
                valor={item.valor}
                porcentagem={item.porcentagem}
                corFundo="#4B5360"
                corPrincipal="#323B40"
              />
            ))}
          </div>
        )}
      </div>
    </>
  );
};

export default ProdutoSituacao;
