import React from 'react';
import { makeStyles } from '@material-ui/core/styles';
import { Typography } from '@material-ui/core';

const useStyles = makeStyles(() => ({
  container: {
    marginBottom: '5px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    padding: '30px 0 30px 0',
    height: '100%',
  },
}));

const GraficoSemDados = () => {
  const classes = useStyles();

  return (
    <div className={classes.container}>
      <Typography variant="body2">
        Não existem informações para serem exibidas
      </Typography>
    </div>
  );
};

export default GraficoSemDados;
