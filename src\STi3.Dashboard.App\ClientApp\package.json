{"name": "sti3-dashboard", "version": "2.0.1", "private": true, "dependencies": {"@date-io/date-fns": "^1.3.13", "@hookform/resolvers": "^1.1.2", "@material-ui/core": "^4.11.0", "@material-ui/icons": "^4.9.1", "@material-ui/lab": "^4.0.0-alpha.56", "@material-ui/pickers": "^3.3.10", "@microsoft/applicationinsights-web": "^2.5.11", "@microsoft/signalr": "^5.0.1", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^11.1.0", "@testing-library/user-event": "^12.1.10", "@types/jest": "^26.0.15", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "apexcharts": "^3.23.1", "axios": "^0.21.1", "bootstrap": "^4.5.3", "cross-env": "^7.0.3", "crypto-js": "^4.0.0", "date-fns": "^2.28.0", "js-file-download": "^0.4.12", "jszip": "^3.10.1", "jwt-decode": "^3.1.2", "notistack": "^1.0.2", "prop-types": "^15.7.2", "react": "^18.0.0", "react-apexcharts": "^1.3.7", "react-dom": "^18.0.0", "react-helmet-async": "^1.3.0", "react-hook-form": "^6.13.0", "react-icons": "^3.11.0", "react-ios-pwa-prompt": "^1.8.4", "react-modern-calendar-datepicker": "^3.1.6", "react-router-dom": "^5.2.0", "react-router-guards": "^1.0.2", "react-spinners": "^0.9.0", "styled-components": "^5.2.1", "typescript": "^4.0.3", "web-vitals": "^0.2.4", "workbox-background-sync": "^5.1.3", "workbox-broadcast-update": "^5.1.3", "workbox-cacheable-response": "^5.1.3", "workbox-core": "^5.1.3", "workbox-expiration": "^5.1.3", "workbox-google-analytics": "^5.1.3", "workbox-navigation-preload": "^5.1.3", "workbox-precaching": "^5.1.3", "workbox-range-requests": "^5.1.3", "workbox-routing": "^5.1.3", "workbox-strategies": "^5.1.3", "workbox-streams": "^5.1.3", "yup": "^0.32.8"}, "scripts": {"dev": "vite", "start": "vite", "build": "tsc && vite build", "preview": "vite preview", "test": "cross-env VITE_APP_SISTEMA=sti3 VITE_API_URL=https://sti3dashboard-homolog-api.azurewebsites.net npm run build"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/crypto-js": "^4.0.1", "@types/node": "^24.0.14", "@types/react-burger-menu": "^2.6.2", "@types/react-router-dom": "^5.1.6", "@types/recharts": "^1.8.16", "@types/styled-components": "^5.1.4", "@typescript-eslint/eslint-plugin": "^4.0.1", "@typescript-eslint/parser": "^4.0.1", "@vitejs/plugin-react": "^4.6.0", "eslint-config-airbnb": "^18.2.0", "eslint-config-airbnb-typescript": "^9.0.0", "eslint-config-prettier": "^6.11.0", "eslint-plugin-jest": "^23.20.0", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-react-hooks": "^4.0.8", "prettier": "^2.0.5", "vite": "^6.3.5"}}