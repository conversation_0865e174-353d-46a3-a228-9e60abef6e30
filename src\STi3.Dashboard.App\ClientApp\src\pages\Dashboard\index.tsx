import React from 'react';
import { Card, Grid, Container } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import ReactApexCharts from 'react-apexcharts';

const useStyles = makeStyles(() => ({
  container: {
    paddingLeft: '0px',
    paddingRight: '0px',
    minWidth: '100%',
    minheight: '100%',
  },
  card: {
    // [theme.breakpoints.down('md')]: {
    //   background: 'red',
    // },
    background: '#4B5360',
    color: '#fff',
    display: 'flex',
    justifyContent: 'center',
  },
  cardHeaderTitle: {
    color: '#fff',
    marginTop: '10px',
    marginLeft: '10px',
    // fontSize: '1.5rem',
  },
  cardHeaderSubtitle: {
    color: '#fff',
    marginLeft: '10px',
    // fontSize: '1.2rem',
    marginTop: '-20px',
  },
}));

const Vendas: React.FC = () => {
  const classes = useStyles();

  const dados = {
    series: [50, 20, 13, 17],
    options: {
      legend: {
        show: true,
        position: 'bottom',
        horizontalAlign: 'center',
        floating: false,
        labels: {
          colors: '#fff',
          useSeriesColors: false,
        },
        itemMargin: {
          horizontal: 10,
          vertical: 5,
        },
        onItemClick: {
          toggleDataSeries: true,
        },
      },
      labels: ['CREDIÁRIO', 'CRÉDITO', 'DÉBITO', 'DINHEIRO'],
      responsive: [
        {
          breakpoint: 450,
          options: {
            chart: {
              width: 300,
            },
            legend: {
              show: true,
              position: 'bottom',
              horizontalAlign: 'center',
              floating: false,
              labels: {
                colors: '#fff',
                useSeriesColors: false,
              },
              itemMargin: {
                horizontal: 10,
                vertical: 5,
              },
              onItemClick: {
                toggleDataSeries: true,
              },
            },
          },
        },
      ],
    },
  };

  const dados2 = {
    series: [
      {
        data: [400, 430, 448, 600, 800, 1500],
      },
    ],

    options: {
      chart: {
        toolbar: {
          show: false,
        },
        foreColor: '#fff',
      },

      legend: {
        show: true,
        position: 'bottom',
        horizontalAlign: 'center',
        floating: false,
        labels: {
          colors: '#fff',
          useSeriesColors: false,
        },
        itemMargin: {
          horizontal: 10,
          vertical: 5,
        },
        onItemClick: {
          toggleDataSeries: true,
        },
      },
      plotOptions: {
        bar: {
          horizontal: false,
        },
      },
      dataLabels: {
        enabled: true,
      },
      xaxis: {
        categories: [
          'Vendedor 1',
          'Vendedor 2',
          'Vendedor 3',
          'Vendedor 4',
          'Vendedor 5',
          'Vendedor 6',
        ],
      },
      // responsive: [
      //   {
      //     breakpoint: 400,
      //     options: {
      //       chart: {
      //         width: 400,
      //       },
      //       plotOptions: {
      //         bar: {
      //           horizontal: true,
      //         },
      //       },
      //     },
      //   },
      // ],
    },
  };

  const dados3 = {
    series: [
      {
        name: 'series1',
        data: [31, 40, 28, 51, 42, 109],
      },
    ],
    options: {
      chart: {
        toolbar: {
          show: false,
        },
        foreColor: '#fff',
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        curve: 'smooth',
      },
      xaxis: {
        type: 'datetime',
        categories: [
          '2018-09-19T00:00:00.000Z',
          '2018-09-19T01:30:00.000Z',
          '2018-09-19T02:30:00.000Z',
          '2018-09-19T03:30:00.000Z',
          '2018-09-19T04:30:00.000Z',
          '2018-09-19T05:30:00.000Z',
        ],
      },
      tooltip: {
        x: {
          format: 'dd/MM/yy HH:mm',
        },
      },
    },
  };

  return (
    <Container className={classes.container}>
      <Grid container spacing={1}>
        <Grid item lg={6} md={6} xl={6} xs={12}>
          <Card className={classes.card}>
            <ReactApexCharts
              options={dados.options as any}
              series={dados.series}
              width="450"
              type="pie"
            />
          </Card>
        </Grid>
        <Grid item lg={6} md={6} xl={6} xs={12}>
          <Card className={classes.card}>
            <ReactApexCharts
              options={dados.options as any}
              series={dados.series}
              width="450"
              type="donut"
            />
          </Card>
        </Grid>
        <Grid item lg={12} md={12} xl={12} xs={12}>
          <Card
            style={{
              background: '#7B67C8',
            }}
          >
            <p className={classes.cardHeaderTitle}>Vendas</p>
            <p className={classes.cardHeaderSubtitle}>
              {`Dados sincronizados em ${new Date().toLocaleString()}`}
            </p>
            <ReactApexCharts
              options={dados2.options as any}
              series={dados2.series}
              type="bar"
              height={300}
            />
          </Card>
        </Grid>
        <Grid item lg={12} md={12} xl={12} xs={12}>
          <Card
            style={{
              background: '#FE6383',
            }}
          >
            <p className={classes.cardHeaderTitle}>Vendas</p>
            <p className={classes.cardHeaderSubtitle}>
              {`Dados sincronizados em ${new Date().toLocaleString()}`}
            </p>
            <ReactApexCharts
              options={dados3.options as any}
              series={dados3.series}
              type="area"
              height={300}
            />
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Vendas;
