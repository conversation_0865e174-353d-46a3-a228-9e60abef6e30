/* eslint-disable radix */
import React, { useState, useEffect, useCallback } from 'react';
import HeaderCard from 'components/HeaderCard';
import RadioButtonFiltros from 'components/RadioButtonFiltros';
import { useChartData } from 'store/ChartDataContext';
import IdentificacaoGrafico from 'constants/identificacaoGrafico';
import ReactApexChart from 'react-apexcharts';
import ApexCharts from 'apexcharts';
import GraficoSemDados from 'components/GraficoSemDados';
import Totalizador from 'components/Totalizador';

const VendaPorHora = () => {
  const { chartData } = useChartData();
  const [dataUltimaSincronizacao, setDataUltimaSincronizacao] = useState('');
  const [radioButtonList, setRadioButtonList] = useState([{}] as any);
  const [infoGraficoState, setInfoGraficoState] = useState<any>(undefined);
  const [totalVendas, setTotalVendas] = useState(0);

  const obterDadosGrafico = (dados) => {
    const informacoesFiltro = dados.filter(
      (item) => item.identificacao === IdentificacaoGrafico.Vendas_VendasPorHora
    );

    if (informacoesFiltro.length > 0) {
      return {
        ...informacoesFiltro[0],
      };
    }

    return null;
  };

  const atualizarGrafico = (list) => {
    const listSort = list.sort(
      (a, b) => parseFloat(a.hora) - parseFloat(b.hora)
    );

    const infoGrafico = {
      series: [
        {
          name: 'Vendas',
          data: [],
        },
      ],
      options: {
        chart: {
          type: 'area',
          id: 'venda-por-hora',
          toolbar: {
            show: false,
          },
          foreColor: '#fff',
        },
        colors: ['#fff'],
        fill: {
          gradient: {
            gradientToColors: ['#fff'],
            opacityFrom: 1,
            opacityTo: 0.5,
          },
        },
        dataLabels: {
          enabled: false,
        },
        stroke: {
          curve: 'smooth',
        },
        markers: {
          size: 5,
          colors: ['#fff'],
          strokeColor: '#fff',
          strokeWidth: 3,
        },
        xaxis: {
          categories: listSort.map((item) => `${item.hora}h`),
          labels: {
            show: true,
            style: {
              colors: ['#fff'],
            },
          },
        },
        grid: {
          borderColor: '#fff',
          yaxis: {
            lines: {
              show: true,
            },
          },
        },
        yaxis: {
          axisBorder: {
            show: false,
          },
          min: 0,
          title: {
            style: {
              color: '#fff',
            },
          },
          labels: {
            show: true,
            style: {
              colors: ['#fff'],
            },
            formatter: (value) => {
              return `${parseInt(value)}`;
            },
          },
        },
        labels: [],
        tooltip: {
          enabled: true,
          theme: 'dark',
          marker: {
            show: false,
          },
        },
      },
    };

    const newInfoGrafico = {
      ...infoGrafico,
      series: [{ name: 'Vendas', data: listSort.map((item) => item.valor) }],
    };

    // Necessário para atualização do gráfico
    try {
      ApexCharts.exec(
        newInfoGrafico.options.chart.id,
        'updateOptions',
        newInfoGrafico.options
      );

      ApexCharts.exec('reactchart-example', 'resetSeries');

      // eslint-disable-next-line no-empty
    } catch (error) {}

    setInfoGraficoState(newInfoGrafico);
  };

  const obterDadosPorFiltro = useCallback(
    (valor) => {
      setInfoGraficoState(undefined);

      const informacao = obterDadosGrafico(chartData);

      const informacaoFiltro = informacao.dados.find(
        (item) => item.Filtro.Valor === valor
      );

      setTotalVendas(informacaoFiltro.Total);

      const Itens = informacaoFiltro.Dados.map((item, index) => {
        return {
          id: index,
          hora: parseInt(item.Descricao),
          valor: parseInt(item.Total),
        };
      });

      atualizarGrafico(Itens);
    },
    [chartData]
  );

  const resetGrafico = () => {
    setRadioButtonList([{}]);
    setTotalVendas(0);
  };

  useEffect(() => {
    const informacao = obterDadosGrafico(chartData);
    if (informacao) {
      setDataUltimaSincronizacao(
        new Date(informacao.dataUltimaSincronizacao).toLocaleString()
      );
      const filtros = informacao.dados.map((item, index) => {
        return {
          texto: item.Filtro.Descricao,
          valor: item.Filtro.Valor,
          possuiDados: item.Total > 0,
          key: index,
        };
      });
      if (filtros) {
        setRadioButtonList(filtros);
        const filtrosComValor = filtros.filter((filtro) => filtro.possuiDados);
        if (filtrosComValor.length > 0) {
          obterDadosPorFiltro(
            filtrosComValor[filtrosComValor.length - 1].valor
          );
        } else {
          resetGrafico();
        }
      }
    } else {
      resetGrafico();
    }
  }, [chartData, obterDadosPorFiltro]);

  return (
    <>
      <HeaderCard
        titulo="Por Hora"
        atualizadoEm={dataUltimaSincronizacao}
        exibirFiltro={false}
        handleOrdem={() => {}}
      />
      <RadioButtonFiltros
        listItems={radioButtonList}
        bgSelecionado="#FE6383"
        bgNaoSelecionado="#993A50"
        handle={obterDadosPorFiltro}
      />

      <Totalizador
        valor={totalVendas}
        titulo="Quantidade de Vendas"
        exibirEmReais={false}
      />

      <div style={{ marginBottom: '5px' }}>
        {totalVendas === 0 && <GraficoSemDados />}
        {totalVendas > 0 && (
          <ReactApexChart
            options={infoGraficoState.options}
            series={infoGraficoState.series}
            type="area"
            height="250px"
          />
        )}
      </div>
    </>
  );
};

export default VendaPorHora;
