import React from 'react';
import TextField, { TextFieldProps } from '@material-ui/core/TextField';
import { colors, makeStyles, withStyles } from '@material-ui/core';

const useStyles = makeStyles(() => ({
  inputLabelProps: {
    color: colors.grey[400],
  },
}));

const CssTextField = withStyles((theme) => ({
  root: {
    '& :not(.Mui-error)': {
      '& label.Mui-focused': {
        color: theme.palette.primary.main,
      },

      '&.MuiOutlinedInput-root': {
        '& fieldset': {
          borderColor: colors.grey[300],
          color: colors.grey[300],
        },
        '&:hover fieldset :not(.Mui-disabled)': {
          borderColor: theme.palette.primary.main,
        },
        '&.Mui-focused fieldset': {
          borderColor: theme.palette.primary.main,
        },
        '& input:disabled': {
          color: colors.grey[200],
          background: '#2e3b42',
        },
      },
    },
    '& .MuiFormLabel-root.Mui-disabled': {
      color: colors.grey[300],
    },
  },
}))(TextField);

const FormInputUncontrolled = (props: TextFieldProps) => {
  const classes = useStyles();

  return (
    <CssTextField
      InputLabelProps={{ className: classes.inputLabelProps }}
      inputProps={{
        onBlur: (e: any) => {
          e.target.value = e.target.value.trim();
        },
      }}
      {...props}
    />
  );
};

export default FormInputUncontrolled;
