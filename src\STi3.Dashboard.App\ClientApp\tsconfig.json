{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": [
      "ES2020",
      "DOM",
      "DOM.Iterable"
    ],
    "module": "ESNext",
    "skipLibCheck": true,
    /* Bundler mode */
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,
    "noImplicitAny": false,
    /* Additional options */
    "allowJs": true,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "baseUrl": ".",
    "paths": {
      "@/*": [
        "./src/*"
      ],
      "components/*": [
        "./src/components/*"
      ],
      "pages/*": [
        "./src/pages/*"
      ],
      "services/*": [
        "./src/services/*"
      ],
      "store/*": [
        "./src/store/*"
      ],
      "types/*": [
        "./src/types/*"
      ],
      "helpers/*": [
        "./src/helpers/*"
      ],
      "constants/*": [
        "./src/constants/*"
      ],
      "assets/*": [
        "./src/assets/*"
      ],
      "icons/*": [
        "./src/icons/*"
      ],
      "styles/*": [
        "./src/styles/*"
      ],
      "theme/*": [
        "./src/theme/*"
      ],
      "auth/*": [
        "./src/auth/*"
      ]
    }
  },
  "include": [
    "src",
    "vite.config.ts"
  ],
  "references": [
    {
      "path": "./tsconfig.node.json"
    }
  ]
}